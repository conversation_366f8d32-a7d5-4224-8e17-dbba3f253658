using Core.Entities;
using System;

namespace Entities.DTOs
{
    public class OwnerForRegisterDto : IDto
    {
        public string FullName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public int CityID { get; set; }
        public int TownID { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? DeletedDate { get; set; }
    }
}
