/* Company Unified Add Wizard Styles */

/* Sticky Header */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1020;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed) var(--transition-timing);
}

[data-theme="dark"] .sticky-header {
  background-color: var(--bg-primary);
  border-bottom-color: var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.main-content {
  padding-top: 0;
}

/* Progress Bar Enhancements */
.progress {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: var(--border-radius-pill);
}

.progress-bar {
  transition: width 0.6s ease;
  border-radius: var(--border-radius-pill);
}

/* Step Navigation Buttons */
.btn {
  transition: all var(--transition-speed) var(--transition-timing);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Form Sections */
.form-section {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all var(--transition-speed) var(--transition-timing);
  border: 1px solid var(--border-color);
}

.form-section:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.form-section-title {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  font-size: 1.1rem;
}

/* Step Navigation Improvements */
.step-nav-btn {
  min-height: 80px;
  text-align: left;
  border: 2px solid transparent;
  transition: all var(--transition-speed) var(--transition-timing);
}

.step-nav-btn.active {
  border-color: var(--primary);
  background-color: var(--primary);
  color: white;
}

.step-nav-btn.completed {
  border-color: var(--success);
  background-color: var(--success-light);
  color: var(--success);
}

.step-nav-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.step-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Wizard Step Animations */
.fade-in {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Validation */
.is-invalid {
  border-color: var(--danger);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

/* Preview Section */
.preview-summary {
  background: linear-gradient(135deg, var(--primary-light), var(--info-light));
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.preview-item {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid var(--primary);
}

/* Loading States */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Dark Mode Enhancements */
[data-theme="dark"] .preview-summary {
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.2), rgba(var(--info-rgb), 0.2));
}

[data-theme="dark"] .step-nav-btn.completed {
  background-color: var(--success-light);
  color: var(--success);
}

[data-theme="dark"] .form-section {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .sticky-header .modern-card-header {
    padding: 0.75rem 1rem;
  }

  .sticky-header .modern-card-header .d-flex.gap-2 {
    flex-direction: row;
    gap: 0.25rem !important;
  }

  .sticky-header .modern-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  .sticky-header h4 {
    font-size: 1.1rem;
  }

  .sticky-header small {
    font-size: 0.75rem;
  }

  .step-nav-btn {
    min-height: 60px;
    font-size: 0.875rem;
  }

  .step-nav-btn .fw-bold {
    font-size: 0.8rem;
  }

  .step-nav-btn small {
    font-size: 0.7rem;
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .row.g-3 {
    --bs-gutter-x: 1rem;
    --bs-gutter-y: 1rem;
  }

  .d-flex.gap-2:not(.sticky-header .d-flex.gap-2) {
    flex-direction: column;
    gap: 0.5rem !important;
  }
}

/* Phone number validation styling */
.phone-valid {
  border-color: var(--success);
}

.phone-invalid {
  border-color: var(--danger);
}

/* Email validation styling */
.email-checking {
  border-color: var(--warning);
}

.email-exists {
  border-color: var(--danger);
}

.email-available {
  border-color: var(--success);
}
