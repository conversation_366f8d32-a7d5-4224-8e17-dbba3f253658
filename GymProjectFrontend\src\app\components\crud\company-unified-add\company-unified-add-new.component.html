<!-- <PERSON><PERSON>er -->
<div class="sticky-header">
  <div class="container-fluid">
    <div class="modern-card mb-4">
      <div class="modern-card-header">
        <div class="d-flex align-items-center">
          <button class="modern-btn modern-btn-outline-secondary me-3" (click)="goBack()">
            <fa-icon [icon]="faArrowLeft"></fa-icon>
          </button>
          <div>
            <h4 class="mb-0">Yeni Salon Ekleme</h4>
            <small class="text-muted">{{getStepTitle()}} - {{getStepDescription()}}</small>
          </div>
        </div>
        <div class="d-flex gap-2">
          <button class="modern-btn modern-btn-secondary" (click)="goBack()">İptal</button>
          <button *ngIf="currentStep > 1" class="modern-btn modern-btn-outline-primary" (click)="previousStep()">
            <fa-icon [icon]="faArrowLeft" class="me-1"></fa-icon>Geri
          </button>
          <button *ngIf="currentStep < 4" class="modern-btn modern-btn-primary" (click)="nextStep()" [disabled]="!canProceedToNextStep()">
            İleri<fa-icon [icon]="faArrowRight" class="ms-1"></fa-icon>
          </button>
          <button *ngIf="currentStep === 4" class="modern-btn modern-btn-success" (click)="onSubmit()" [disabled]="isSubmitting">
            <fa-icon [icon]="faSave" class="me-1"></fa-icon>{{isSubmitting ? 'Kaydediliyor...' : 'Salonu Kaydet'}}
          </button>
        </div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress mb-4" style="height: 6px;">
      <div class="progress-bar bg-primary" [style.width.%]="getProgressPercentage()"></div>
    </div>

    <!-- Step Navigation -->
    <div class="row g-2 mb-4">
      <div class="col">
        <button class="btn w-100 text-start" [class]="getStepButtonClass(1)" (click)="goToStep(1)">
          <div class="d-flex align-items-center">
            <div class="me-2"><fa-icon [icon]="faBuilding"></fa-icon></div>
            <div>
              <div class="fw-bold">1. Şirket Bilgileri</div>
              <small class="text-muted">Ad ve telefon</small>
            </div>
          </div>
        </button>
      </div>
      <div class="col">
        <button class="btn w-100 text-start" [class]="getStepButtonClass(2)" (click)="goToStep(2)">
          <div class="d-flex align-items-center">
            <div class="me-2"><fa-icon [icon]="faMapMarkedAlt"></fa-icon></div>
            <div>
              <div class="fw-bold">2. Adres Bilgileri</div>
              <small class="text-muted">İl, ilçe, adres</small>
            </div>
          </div>
        </button>
      </div>
      <div class="col">
        <button class="btn w-100 text-start" [class]="getStepButtonClass(3)" (click)="goToStep(3)">
          <div class="d-flex align-items-center">
            <div class="me-2"><fa-icon [icon]="faUserTie"></fa-icon></div>
            <div>
              <div class="fw-bold">3. Salon Sahibi</div>
              <small class="text-muted">Kişisel bilgiler</small>
            </div>
          </div>
        </button>
      </div>
      <div class="col">
        <button class="btn w-100 text-start" [class]="getStepButtonClass(4)" (click)="goToStep(4)">
          <div class="d-flex align-items-center">
            <div class="me-2"><fa-icon [icon]="faEye"></fa-icon></div>
            <div>
              <div class="fw-bold">4. Önizleme</div>
              <small class="text-muted">Kontrol & kaydet</small>
            </div>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Step Content -->
<div class="container-fluid">
  <!-- Step 1: Company Info -->
  <div *ngIf="currentStep === WizardStep.COMPANY_INFO" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>Şirket Bilgileri
      </h5>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="companyInfoForm">
        <div class="row g-3">
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">Şirket Adı <span class="text-danger">*</span></label>
              <input 
                type="text" 
                class="modern-form-control" 
                formControlName="companyName" 
                placeholder="Şirket adını giriniz"
                [class.is-invalid]="companyInfoForm.get('companyName')?.invalid && companyInfoForm.get('companyName')?.touched">
              <div class="invalid-feedback" *ngIf="companyInfoForm.get('companyName')?.invalid && companyInfoForm.get('companyName')?.touched">
                <div *ngIf="companyInfoForm.get('companyName')?.hasError('required')">Şirket adı zorunludur</div>
                <div *ngIf="companyInfoForm.get('companyName')?.hasError('minlength')">En az 2 karakter olmalıdır</div>
                <div *ngIf="companyInfoForm.get('companyName')?.hasError('maxlength')">En fazla 100 karakter olmalıdır</div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">Şirket Telefonu <span class="text-danger">*</span></label>
              <input 
                type="text" 
                class="modern-form-control" 
                formControlName="companyPhone" 
                placeholder="05xxxxxxxxx"
                [class.is-invalid]="companyInfoForm.get('companyPhone')?.invalid && companyInfoForm.get('companyPhone')?.touched">
              <div class="invalid-feedback" *ngIf="companyInfoForm.get('companyPhone')?.invalid && companyInfoForm.get('companyPhone')?.touched">
                <div *ngIf="companyInfoForm.get('companyPhone')?.hasError('required')">Telefon numarası zorunludur</div>
                <div *ngIf="companyInfoForm.get('companyPhone')?.hasError('pattern')">11 haneli olmalı ve 0 ile başlamalıdır</div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 2: Address Info -->
  <div *ngIf="currentStep === WizardStep.ADDRESS_INFO" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faMapMarkedAlt" class="me-2"></fa-icon>Adres Bilgileri
      </h5>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="addressInfoForm">
        <div class="row g-3">
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">İl <span class="text-danger">*</span></label>
              <input 
                type="text" 
                class="modern-form-control" 
                formControlName="city" 
                placeholder="İl seçiniz"
                [matAutocomplete]="autoCity"
                [class.is-invalid]="addressInfoForm.get('city')?.invalid && addressInfoForm.get('city')?.touched">
              <mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCity">
                <mat-option *ngFor="let city of filteredCities | async" [value]="city">
                  {{city.cityName}}
                </mat-option>
              </mat-autocomplete>
              <div class="invalid-feedback" *ngIf="addressInfoForm.get('city')?.invalid && addressInfoForm.get('city')?.touched">
                İl seçimi zorunludur
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">İlçe <span class="text-danger">*</span></label>
              <input 
                type="text" 
                class="modern-form-control" 
                formControlName="town" 
                placeholder="İlçe seçiniz"
                [matAutocomplete]="autoTown"
                [class.is-invalid]="addressInfoForm.get('town')?.invalid && addressInfoForm.get('town')?.touched">
              <mat-autocomplete #autoTown="matAutocomplete" [displayWith]="displayTown">
                <mat-option *ngFor="let town of filteredTowns | async" [value]="town">
                  {{town.townName}}
                </mat-option>
              </mat-autocomplete>
              <div class="invalid-feedback" *ngIf="addressInfoForm.get('town')?.invalid && addressInfoForm.get('town')?.touched">
                İlçe seçimi zorunludur
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="modern-form-group">
              <label class="modern-form-label">Detay Adres <span class="text-danger">*</span></label>
              <textarea 
                class="modern-form-control" 
                formControlName="address" 
                placeholder="Detay adres giriniz"
                rows="3"
                [class.is-invalid]="addressInfoForm.get('address')?.invalid && addressInfoForm.get('address')?.touched"></textarea>
              <div class="invalid-feedback" *ngIf="addressInfoForm.get('address')?.invalid && addressInfoForm.get('address')?.touched">
                <div *ngIf="addressInfoForm.get('address')?.hasError('required')">Adres zorunludur</div>
                <div *ngIf="addressInfoForm.get('address')?.hasError('minlength')">En az 5 karakter olmalıdır</div>
                <div *ngIf="addressInfoForm.get('address')?.hasError('maxlength')">En fazla 200 karakter olmalıdır</div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 3: Owner Info -->
  <div *ngIf="currentStep === WizardStep.OWNER_INFO" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>Salon Sahibi Bilgileri
      </h5>
    </div>
    <div class="modern-card-body">
      <form [formGroup]="ownerInfoForm">
        <div class="row g-3">
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">Ad Soyad <span class="text-danger">*</span></label>
              <input
                type="text"
                class="modern-form-control"
                formControlName="ownerFullName"
                placeholder="Ad soyad giriniz"
                [class.is-invalid]="ownerInfoForm.get('ownerFullName')?.invalid && ownerInfoForm.get('ownerFullName')?.touched">
              <div class="invalid-feedback" *ngIf="ownerInfoForm.get('ownerFullName')?.invalid && ownerInfoForm.get('ownerFullName')?.touched">
                <div *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('required')">Ad soyad zorunludur</div>
                <div *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('minlength')">En az 2 karakter olmalıdır</div>
                <div *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('maxlength')">En fazla 100 karakter olmalıdır</div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="modern-form-group">
              <label class="modern-form-label">Telefon Numarası <span class="text-danger">*</span></label>
              <input
                type="text"
                class="modern-form-control"
                formControlName="ownerPhone"
                placeholder="05xxxxxxxxx"
                [class.is-invalid]="ownerInfoForm.get('ownerPhone')?.invalid && ownerInfoForm.get('ownerPhone')?.touched">
              <div class="invalid-feedback" *ngIf="ownerInfoForm.get('ownerPhone')?.invalid && ownerInfoForm.get('ownerPhone')?.touched">
                <div *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('required')">Telefon numarası zorunludur</div>
                <div *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('pattern')">11 haneli olmalı ve 0 ile başlamalıdır</div>
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="modern-form-group">
              <label class="modern-form-label">E-posta <span class="text-danger">*</span></label>
              <input
                type="email"
                class="modern-form-control"
                formControlName="ownerEmail"
                placeholder="E-posta adresini giriniz"
                [class.is-invalid]="ownerInfoForm.get('ownerEmail')?.invalid && ownerInfoForm.get('ownerEmail')?.touched"
                [class.email-exists]="ownerInfoForm.get('ownerEmail')?.hasError('emailExists')">
              <div class="invalid-feedback" *ngIf="ownerInfoForm.get('ownerEmail')?.invalid && ownerInfoForm.get('ownerEmail')?.touched">
                <div *ngIf="ownerInfoForm.get('ownerEmail')?.hasError('required')">E-posta zorunludur</div>
                <div *ngIf="ownerInfoForm.get('ownerEmail')?.hasError('email')">Geçerli bir e-posta adresi giriniz</div>
                <div *ngIf="ownerInfoForm.get('ownerEmail')?.hasError('emailExists')">Bu e-posta adresi zaten sistemde kayıtlı</div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Step 4: Preview -->
  <div *ngIf="currentStep === WizardStep.PREVIEW" class="modern-card mb-4 fade-in">
    <div class="modern-card-header">
      <h5 class="mb-0">
        <fa-icon [icon]="faEye" class="me-2"></fa-icon>Önizleme & Kaydet
      </h5>
    </div>
    <div class="modern-card-body">
      <div class="preview-summary">
        <div class="row g-3">
          <div class="col-md-4">
            <div class="preview-item">
              <h6 class="mb-2">
                <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>Şirket Bilgileri
              </h6>
              <p class="mb-1"><strong>Şirket Adı:</strong> {{formData.companyName}}</p>
              <p class="mb-0"><strong>Telefon:</strong> {{formData.companyPhone}}</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="preview-item">
              <h6 class="mb-2">
                <fa-icon [icon]="faMapMarkedAlt" class="me-2"></fa-icon>Adres Bilgileri
              </h6>
              <p class="mb-1"><strong>İl/İlçe:</strong> {{formData.city?.cityName}}/{{formData.town?.townName}}</p>
              <p class="mb-0"><strong>Adres:</strong> {{formData.address}}</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="preview-item">
              <h6 class="mb-2">
                <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>Salon Sahibi
              </h6>
              <p class="mb-1"><strong>Ad Soyad:</strong> {{formData.ownerFullName}}</p>
              <p class="mb-1"><strong>E-posta:</strong> {{formData.ownerEmail}}</p>
              <p class="mb-0"><strong>Telefon:</strong> {{formData.ownerPhone}}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="alert alert-info d-flex align-items-center">
        <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
        <div>
          <strong>Bilgilendirme:</strong> Salon sahibi için otomatik hesap oluşturulacaktır.
          Geçici şifre telefon numarasının son 4 hanesi olacaktır.
        </div>
      </div>
    </div>
  </div>
</div>
