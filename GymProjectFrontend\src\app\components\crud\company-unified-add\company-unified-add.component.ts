import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CompanyService } from '../../../services/company.service';
import { CompanyadressService } from '../../../services/companyadress.service';
import { CompanyUserService } from '../../../services/company-user.service';
import { UserCompanyService } from '../../../services/usercompany.service';
import { CityService } from '../../../services/city.service';
import { TownService } from '../../../services/town.service';
import { AuthService } from '../../../services/auth.service';
import { City } from '../../../models/city';
import { Town } from '../../../models/town';
import { Company } from '../../../models/company';
import { CompanyAdress } from '../../../models/companyAdress';
import { CompanyUser } from '../../../models/companyUser';
import { UserCompany } from '../../../models/usercompany';
import {
  faArrowLeft,
  faArrowRight,
  faSave,
  faBuilding,
  faMapMarkedAlt,
  faUserTie,
  faEye,
  faCheck,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';

// Wizard adımları enum'u
enum WizardStep {
  COMPANY_INFO = 1,
  ADDRESS_INFO = 2,
  OWNER_INFO = 3,
  PREVIEW = 4
}

@Component({
  selector: 'app-company-unified-add',
  templateUrl: './company-unified-add.component.html',
  styleUrls: ['./company-unified-add.component.css'],
  standalone: false
})
export class CompanyUnifiedAddComponent implements OnInit {
  // Icons
  faArrowLeft = faArrowLeft;
  faArrowRight = faArrowRight;
  faSave = faSave;
  faBuilding = faBuilding;
  faMapMarkedAlt = faMapMarkedAlt;
  faUserTie = faUserTie;
  faEye = faEye;
  faCheck = faCheck;
  faInfoCircle = faInfoCircle;

  // Wizard state
  currentStep: WizardStep = WizardStep.COMPANY_INFO;
  WizardStep = WizardStep; // Template'de kullanmak için

  // Forms
  companyInfoForm!: FormGroup;
  addressInfoForm!: FormGroup;
  ownerInfoForm!: FormGroup;
  isSubmitting = false;

  // Data
  cities: City[] = [];
  towns: Town[] = [];
  filteredCities: Observable<City[]>;
  filteredTowns: Observable<Town[]>;

  // Basit ilerleme sistemi
  completedSteps: Set<WizardStep> = new Set();

  // Form data storage
  formData: any = {};

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private companyService: CompanyService,
    private companyAdressService: CompanyadressService,
    private companyUserService: CompanyUserService,
    private userCompanyService: UserCompanyService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.getCities();
    this.getTowns();
  }

  initializeForms(): void {
    // Adım 1: Şirket bilgileri formu
    this.companyInfoForm = this.formBuilder.group({
      companyName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });

    // Adım 2: Adres bilgileri formu
    this.addressInfoForm = this.formBuilder.group({
      city: ['', Validators.required],
      town: ['', Validators.required],
      address: ['', [Validators.required, Validators.minLength(5), Validators.maxLength(200)]]
    });

    // Adım 3: Salon sahibi bilgileri formu
    this.ownerInfoForm = this.formBuilder.group({
      ownerFullName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      ownerEmail: ['', [Validators.required, Validators.email]],
      ownerPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });
  }

  // Wizard navigation methods
  nextStep(): void {
    if (this.canProceedToNextStep()) {
      this.completedSteps.add(this.currentStep);
      if (this.currentStep < WizardStep.PREVIEW) {
        this.currentStep++;
        this.onStepChange();
      }
    } else {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.validateAndHighlightErrors();
    }
  }

  previousStep(): void {
    if (this.currentStep > WizardStep.COMPANY_INFO) {
      this.currentStep--;
    }
  }

  goToStep(step: WizardStep): void {
    if (this.canGoToStep(step)) {
      this.currentStep = step;
      this.onStepChange();
    }
  }

  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return this.companyInfoForm.valid;
      case WizardStep.ADDRESS_INFO:
        return this.addressInfoForm.valid;
      case WizardStep.OWNER_INFO:
        return this.ownerInfoForm.valid;
      default:
        return true;
    }
  }

  canGoToStep(step: WizardStep): boolean {
    if (step <= this.currentStep) return true;
    if (step === this.currentStep + 1) return this.canProceedToNextStep();
    return this.completedSteps.has(step - 1);
  }

  onStepChange(): void {
    switch (this.currentStep) {
      case WizardStep.ADDRESS_INFO:
        this.saveCompanyInfo();
        this.setupAddressFilters();
        break;
      case WizardStep.OWNER_INFO:
        this.saveAddressInfo();
        break;
      case WizardStep.PREVIEW:
        this.saveOwnerInfo();
        this.preparePreviewData();
        break;
    }
  }

  validateAndHighlightErrors(): void {
    const currentForm = this.getCurrentForm();
    if (currentForm) {
      this.markFormGroupTouched(currentForm);
    }
  }

  getCurrentForm(): FormGroup | null {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return this.companyInfoForm;
      case WizardStep.ADDRESS_INFO:
        return this.addressInfoForm;
      case WizardStep.OWNER_INFO:
        return this.ownerInfoForm;
      default:
        return null;
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Data saving methods
  saveCompanyInfo(): void {
    if (this.companyInfoForm.valid) {
      this.formData = {
        ...this.formData,
        ...this.companyInfoForm.value
      };
    }
  }

  saveAddressInfo(): void {
    if (this.addressInfoForm.valid) {
      this.formData = {
        ...this.formData,
        ...this.addressInfoForm.value
      };
    }
  }

  saveOwnerInfo(): void {
    if (this.ownerInfoForm.valid) {
      this.formData = {
        ...this.formData,
        ...this.ownerInfoForm.value
      };
    }
  }

  preparePreviewData(): void {
    this.saveOwnerInfo();
  }

  setupAddressFilters(): void {
    this.filteredCities = this.addressInfoForm.get('city')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.cityName),
      map(name => name ? this._filterCities(name) : this.cities.slice())
    );

    this.filteredTowns = this.addressInfoForm.get('town')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.townName),
      map(name => name ? this._filterTowns(name) : this.getFilteredTowns())
    );
  }

  getFilteredTowns(): Town[] {
    const selectedCity = this.addressInfoForm.get('city')?.value;
    if (selectedCity && selectedCity.cityID) {
      return this.towns.filter(town => town.cityID === selectedCity.cityID);
    }
    return this.towns;
  }

  // Final submission
  onSubmit(): void {
    if (this.canSubmitProgram()) {
      this.isSubmitting = true;

      // Email kontrolü
      const ownerEmail = this.formData.ownerEmail;
      this.authService.userExists(ownerEmail).subscribe(
        (userExistsResponse) => {
          if (!userExistsResponse.success) {
            // Email zaten var - 3. adıma git
            this.currentStep = WizardStep.OWNER_INFO;
            this.ownerInfoForm.get('ownerEmail')?.setErrors({ emailExists: true });
            this.toastrService.error('Bu e-posta adresi zaten sistemde kayıtlı', 'Hata');
            this.isSubmitting = false;
            return;
          }

          // Email mevcut değil, kayıt işlemine devam et
          this.processRegistration();
        },
        (error) => {
          this.toastrService.error('Email kontrolü yapılamadı', 'Hata');
          this.isSubmitting = false;
        }
      );
    }
  }

  private processRegistration(): void {
    // 1. RegisterOwner API çağrısı
    const ownerRegisterData = {
      registerDto: {
        fullName: this.formData.ownerFullName,
        email: this.formData.ownerEmail,
        phoneNumber: this.formData.ownerPhone,
        cityID: this.formData.city.cityID,
        townID: this.formData.town.townID
      }
    };

    this.authService.registerOwner(ownerRegisterData).subscribe(
      (ownerResponse) => {
        if (ownerResponse.success) {
          const userId = ownerResponse.data.userId;

          // 2. Company kaydet
          this.registerCompany(userId);
        } else {
          this.handleRegistrationError(WizardStep.OWNER_INFO, 'Salon sahibi kaydedilemedi: ' + ownerResponse.message);
        }
      },
      (error) => {
        this.handleRegistrationError(WizardStep.OWNER_INFO, 'Salon sahibi kayıt hatası: ' + error.message);
      }
    );
  }

  private registerCompany(userId: number): void {
    const companyModel = {
      companyName: this.formData.companyName,
      phoneNumber: this.formData.companyPhone,
      isActive: true
    } as Company;

    this.companyService.add(companyModel).subscribe(
      (companyResponse) => {
        if (companyResponse.success) {
          // Company ID'sini al
          this.getCompanyId(companyModel.companyName, userId);
        } else {
          this.handleRegistrationError(WizardStep.COMPANY_INFO, 'Şirket kaydedilemedi');
        }
      },
      (error) => {
        this.handleRegistrationError(WizardStep.COMPANY_INFO, 'Şirket kayıt hatası');
      }
    );
  }

  private getCompanyId(companyName: string, userId: number): void {
    this.companyService.getCompanies().subscribe(
      (companiesResponse) => {
        const companies = companiesResponse.data;
        const addedCompany = companies.find(c => c.companyName === companyName);

        if (addedCompany) {
          this.registerAddress(addedCompany.companyID, userId);
        } else {
          this.handleRegistrationError(WizardStep.COMPANY_INFO, 'Eklenen şirket bulunamadı');
        }
      },
      (error) => {
        this.handleRegistrationError(WizardStep.COMPANY_INFO, 'Şirket bilgileri alınamadı');
      }
    );
  }

  private registerAddress(companyId: number, userId: number): void {
    const addressModel = {
      companyID: companyId,
      cityID: this.formData.city.cityID,
      townID: this.formData.town.townID,
      adress: this.formData.address
    } as CompanyAdress;

    this.companyAdressService.add(addressModel).subscribe(
      (addressResponse) => {
        if (addressResponse.success) {
          this.registerCompanyUser(companyId, userId);
        } else {
          this.handleRegistrationError(WizardStep.ADDRESS_INFO, 'Adres kaydedilemedi');
        }
      },
      (error) => {
        this.handleRegistrationError(WizardStep.ADDRESS_INFO, 'Adres kayıt hatası');
      }
    );
  }

  private registerCompanyUser(companyId: number, userId: number): void {
    const ownerModel = {
      cityID: this.formData.city.cityID,
      townID: this.formData.town.townID,
      name: this.formData.ownerFullName,
      phoneNumber: this.formData.ownerPhone,
      email: this.formData.ownerEmail
    } as CompanyUser;

    this.companyUserService.add(ownerModel).subscribe(
      (ownerResponse) => {
        if (ownerResponse.success) {
          // CompanyUser ID'sini al
          this.getCompanyUserId(this.formData.ownerEmail, companyId, userId);
        } else {
          this.handleRegistrationError(WizardStep.OWNER_INFO, 'Şirket sahibi bilgileri kaydedilemedi');
        }
      },
      (error) => {
        this.handleRegistrationError(WizardStep.OWNER_INFO, 'Şirket sahibi kayıt hatası');
      }
    );
  }

  private getCompanyUserId(ownerEmail: string, companyId: number, userId: number): void {
    this.companyUserService.getCompanyUserDetails().subscribe(
      (companyUsersResponse) => {
        const companyUsers = companyUsersResponse.data;
        const addedCompanyUser = companyUsers.find(cu => cu.companyUserEmail === ownerEmail);

        if (addedCompanyUser) {
          this.createUserCompanyRelation(companyId, userId, addedCompanyUser.companyUserId);
        } else {
          this.handleRegistrationError(WizardStep.OWNER_INFO, 'Eklenen şirket sahibi bulunamadı');
        }
      },
      (error) => {
        this.handleRegistrationError(WizardStep.OWNER_INFO, 'Şirket sahibi bilgileri alınamadı');
      }
    );
  }

  private createUserCompanyRelation(companyId: number, userId: number, companyUserId: number): void {
    const relationModel = {
      userID: userId,
      companyUserID: companyUserId
    } as UserCompany;

    this.userCompanyService.add(relationModel).subscribe(
      (relationResponse) => {
        if (relationResponse.success) {
          this.toastrService.success('Salon başarıyla eklendi', 'Başarılı');
          this.resetForm();
          this.isSubmitting = false;
        } else {
          this.handleRegistrationError(WizardStep.PREVIEW, 'İlişki kurulamadı');
        }
      },
      (error) => {
        this.handleRegistrationError(WizardStep.PREVIEW, 'İlişki kurma hatası');
      }
    );
  }

  private handleRegistrationError(step: WizardStep, message: string): void {
    this.currentStep = step;
    this.toastrService.error(message, 'Hata');
    this.isSubmitting = false;
  }

  canSubmitProgram(): boolean {
    return this.isStepCompleted(WizardStep.COMPANY_INFO) &&
           this.isStepCompleted(WizardStep.ADDRESS_INFO) &&
           this.isStepCompleted(WizardStep.OWNER_INFO);
  }

  isStepCompleted(step: WizardStep): boolean {
    return this.completedSteps.has(step);
  }

  // Utility methods
  getCities() {
    this.cityService.getCities().subscribe((response) => {
      this.cities = response.data;
    });
  }

  getTowns() {
    this.townService.getTowns().subscribe((response) => {
      this.towns = response.data;
    });
  }

  displayCity(city: City): string {
    return city && city.cityName ? city.cityName : '';
  }

  displayTown(town: Town): string {
    return town && town.townName ? town.townName : '';
  }

  private _filterCities(name: string): City[] {
    const filterValue = name.toLowerCase();
    return this.cities.filter(city => city.cityName.toLowerCase().includes(filterValue));
  }

  private _filterTowns(name: string): Town[] {
    const filterValue = name.toLowerCase();
    return this.towns.filter(town => town.townName.toLowerCase().includes(filterValue));
  }

  resetForm() {
    this.companyInfoForm.reset();
    this.addressInfoForm.reset();
    this.ownerInfoForm.reset();
    this.formData = {};
    this.currentStep = WizardStep.COMPANY_INFO;
    this.completedSteps.clear();
  }

  goBack(): void {
    this.router.navigate(['/owner-panel']);
  }

  // Step title and description methods
  getStepTitle(): string {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return 'Şirket Bilgileri';
      case WizardStep.ADDRESS_INFO:
        return 'Adres Bilgileri';
      case WizardStep.OWNER_INFO:
        return 'Salon Sahibi Bilgileri';
      case WizardStep.PREVIEW:
        return 'Önizleme & Kaydet';
      default:
        return '';
    }
  }

  getStepDescription(): string {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return 'Salon şirket bilgilerini giriniz';
      case WizardStep.ADDRESS_INFO:
        return 'Salon adres bilgilerini giriniz';
      case WizardStep.OWNER_INFO:
        return 'Salon sahibi bilgilerini giriniz';
      case WizardStep.PREVIEW:
        return 'Bilgileri kontrol edin ve kaydedin';
      default:
        return '';
    }
  }

  getProgressPercentage(): number {
    return (this.currentStep / 4) * 100;
  }

  getStepButtonClass(step: WizardStep): string {
    if (step === this.currentStep) {
      return 'btn btn-primary step-nav-btn active';
    } else if (this.completedSteps.has(step)) {
      return 'btn btn-success step-nav-btn completed';
    } else {
      return 'btn btn-outline-secondary step-nav-btn';
    }
  }

  getStepIcon(step: WizardStep): any {
    switch (step) {
      case WizardStep.COMPANY_INFO:
        return this.faBuilding;
      case WizardStep.ADDRESS_INFO:
        return this.faMapMarkedAlt;
      case WizardStep.OWNER_INFO:
        return this.faUserTie;
      case WizardStep.PREVIEW:
        return this.faEye;
      default:
        return this.faInfoCircle;
    }
  }
}
