# Company/Unified-Add Panel Yeniden Tasarım Prompt

## GÖREV TANIMI:
GymProject'te company/unified-add panelini workout-programs/add panelindeki gibi 4 adımlı wizard sistemine dönüştür. Salon sahibi kullanıcılarını otomatik olarak sisteme kaydetme özelliği ekle.

## MEVCUT DURUM:
- company/unified-add panelinde şirket, adres ve salon sahibi bilgileri tek sayfada
- Salon sahibi email'i mevcut User tablosundan autocomplete ile seçiliyor
- Bu yaklaşım kullanıcı deneyimi açısından kötü (önce kayıt olma zorunluluğu)

## HEDEF DURUM:
- 4 adımlı wizard sistemi (workout-programs/add benzeri)
- Salon sahibi bilgileri manuel girile<PERSON>k, otomatik User hesabı oluşturulacak
- Geçici şifre: telefon numarasının son 4 hanesi
- Transaction-based kayıt sistemi (hata durumunda rollback)

## VERİ YAPISI ANALİZİ:

### User Tablosu:
- FirstName (string) - <PERSON><PERSON><PERSON> alan
- LastName (string) - Ayrı alan  
- Email (string)
- PasswordHash, PasswordSalt
- RequirePasswordChange (bool)

### CompanyUser Tablosu:
- Name (string) - Birleşik alan (Ad + Soyad)
- Email (string)
- PhoneNumber (string)
- CityID, TownID

### Ad-Soyad Parse Mantığı:
Frontend'de tek alan: "Ali Ayşen Veli Kayseri Bol"
Backend'de parse:
- FirstName: "Ali Ayşen Veli Kayseri" (son kelime hariç tümü)
- LastName: "Bol" (son kelime)
- CompanyUser.Name: "Ali Ayşen Veli Kayseri Bol" (tam hali)

## 4 ADIMLI WİZARD TASARIMI:

### Adım 1: Şirket Bilgileri
- Şirket Adı (zorunlu)
- Şirket Telefonu (zorunlu, 11 haneli, 0 ile başlamalı - real-time validasyon)

### Adım 2: Adres Bilgileri
- İl (autocomplete)
- İlçe (cascading dropdown - il seçildikten sonra)
- Detay Adres (textarea)

### Adım 3: Salon Sahibi Bilgileri
- Ad Soyad (tek alan)
- E-posta (email format validasyonu)
- Telefon Numarası (11 haneli, 0 ile başlamalı - real-time validasyon)
- Email çakışması kontrolü YOK (sadece kaydet sırasında)

### Adım 4: Önizleme & Kaydet
- Tüm bilgilerin özeti
- "SALONU KAYDET" butonu

## BACKEND İŞLEM SIRASI (Transaction):

### Kaydet Butonuna Basıldığında:
1. Loading başlat ("Salon kaydediliyor...")
2. Email kontrolü (_authService.UserExists)
   - Varsa → 3. adıma git, email hatası göster
3. RegisterOwner API → Yeni User oluştur
   - Ad-soyad parse et
   - Şifre: telefon son 4 hanesi
   - RequirePasswordChange: true
4. Company kaydet
5. CompanyAddress kaydet
6. CompanyUser kaydet
7. UserCompany ilişkisi kur

### Hata Durumları (Rollback):
- 3. adımda hata → 3. adıma git, email hatası
- 4. adımda hata → 1. adıma git, şirket hatası + User sil
- 5. adımda hata → 2. adıma git, adres hatası + User & Company sil
- 6. adımda hata → 3. adıma git, salon sahibi hatası + önceki kayıtları sil
- 7. adımda hata → Genel hata, tüm kayıtları sil

## FRONTEND TASARIM REQUİREMENTS:

### Sticky Header:
```
[← Geri] Yeni Salon Ekleme - [Adım Başlığı]     [İptal] [Geri] [İleri/Kaydet]
```

### Progress Bar:
- Adım ilerlemesini gösteren progress bar

### Step Navigation:
- 4 buton: Şirket, Adres, Salon Sahibi, Önizleme
- Tamamlanan adımlar yeşil, aktif mavi, bekleyenler gri

### Validasyon:
- Real-time: Telefon formatı (11 haneli, 0 ile başlamalı)
- Submit-time: Email çakışması kontrolü
- Form geçişlerinde: Zorunlu alan kontrolü

## BACKEND DOSYALARI:

### Yeni Dosyalar:
1. `OwnerForRegisterDto.cs`:
```csharp
public class OwnerForRegisterDto : IDto
{
    public string FullName { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public int CityID { get; set; }
    public int TownID { get; set; }
}
```

2. `AuthManager.RegisterOwner()` metodu
3. `AuthController.RegisterOwner` endpoint

### Parse Algoritması:
```csharp
var nameParts = fullName.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
if (nameParts.Length == 1)
{
    firstName = nameParts[0];
    lastName = "";
}
else
{
    lastName = nameParts[nameParts.Length - 1];
    firstName = string.Join(" ", nameParts.Take(nameParts.Length - 1));
}
```

## FRONTEND DOSYALARI:

### Değiştirilecek Dosyalar:
1. `company-unified-add.component.ts` - Tamamen yeniden yaz
2. `company-unified-add.component.html` - Wizard yapısı
3. `company-unified-add.component.css` - workout-programs stilini kopyala

### Component Yapısı:
- currentStep: number (1-4)
- 4 ayrı FormGroup (companyForm, addressForm, ownerForm)
- Step navigation metodları
- Validasyon metodları
- Transaction-based submit

## ÖZEL NOTLAR:

### Tasarım:
- workout-programs/add panelindeki EXACT tasarımı kullan
- Dark mode uyumlu CSS
- Responsive tasarım
- Modern card yapısı

### Validasyon Kuralları:
- Telefon: 11 haneli, 0 ile başlamalı
- Email: Format kontrolü + çakışma kontrolü
- Zorunlu alanlar: Real-time highlight

### Transaction Yönetimi:
- Backend'de TransactionScope kullan
- Hata durumunda önceki işlemleri geri al
- Kullanıcıyı hatalı adıma yönlendir

### Kullanıcı Deneyimi:
- Loading states
- Progress indicators
- Clear error messages
- Step-by-step guidance

## BAŞARI KRİTERLERİ:
1. ✅ 4 adımlı wizard çalışıyor
2. ✅ Real-time validasyon aktif
3. ✅ Email çakışması kontrolü çalışıyor
4. ✅ Ad-soyad parse işlemi doğru
5. ✅ Transaction rollback çalışıyor
6. ✅ Dark mode uyumlu tasarım
7. ✅ Hata durumlarında doğru adıma yönlendirme
8. ✅ Geçici şifre sistemi çalışıyor

## UYGULAMA SIRASI:
1. Backend: OwnerForRegisterDto + RegisterOwner metodu
2. Backend: AuthController endpoint
3. Frontend: Component refactor (wizard yapısı)
4. Frontend: Validasyon sistemi
5. Frontend: Transaction handling
6. Test: Tüm senaryoları test et
7. Salon listesi ayrı sayfaya taşı

Bu prompt'u AI'ya verdiğinde, tüm detayları bilerek projeyi sorunsuz tamamlayabilir.
